use std::io::Cursor;
use std::sync::Arc;

use reactive_state::middleware::{Middleware, ReduceFn, ReduceMiddlewareResult};

use pianorhythm_proto::pianorhythm_actions::{AppStateActions, AppStateActions_Action};
use pianorhythm_proto::pianorhythm_app_renditions::{AppMidiSequencerEvent, AppMidiSequencerEventType};
use pianorhythm_proto::pianorhythm_effects::{AppStateEffects, AppStateEffects_Action, AppStateEffects_BytesPayload};
use pianorhythm_proto::pianorhythm_events::AppStateEvents;
#[cfg(feature = "use_synth")]
use pianorhythm_synth::{MidiFile, VPSheetMusicFile};

#[cfg(feature = "use_synth")]
use crate::{load_vp_sheet, MIDI_SEQUENCER, VP_SHEET_SEQUENCER};
use crate::reducers::app_state::AppState;

pub struct HandleVPSequencerActionsMiddleware<'c> {
    pub core_api: &'c crate::types::CoreClientApiType,
}

impl<'c> Middleware<AppState, AppStateActions, AppStateEvents, AppStateEffects> for HandleVPSequencerActionsMiddleware<'c> {
    fn on_reduce(
        &self, store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>, action: Option<&AppStateActions>,
        reduce: ReduceFn<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
    ) -> ReduceMiddlewareResult<AppStateEvents, AppStateEffects> {
        #[cfg(feature = "use_synth")]
        if let Some(action) = action {
            match action.action {
                AppStateActions_Action::VPSequencerDownloadAsMidi |
                AppStateActions_Action::VPSequencerLoadData if action.has_vpFileLoad() => {
                    let file = action.get_vpFileLoad();

                    if file.get_tracks().is_empty() {
                        return ReduceMiddlewareResult::default();
                    }

                    if let Some(data) = load_vp_sheet(VPSheetMusicFile {
                        tracks: Arc::new(vec![file.get_data().to_string()]),
                        bpm: file.get_tracks().first().map(|x| x.get_tempo() as usize),
                        file_name: Some(file.get_fileName().to_string()),
                        ..Default::default()
                    }, action.action == AppStateActions_Action::VPSequencerDownloadAsMidi) {
                        self.core_api.dispatch_app_effect(&pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::MidiSequencerDownloadMidi, |effect| {
                            let mut payload = AppStateEffects_BytesPayload::new();
                            payload.set_payload(data.iter().map(|x| *x as u32).collect());
                            payload.set_id(file.get_fileName().to_string());
                            effect.set_bytesPayload(payload);
                        }));
                    };
                }
                AppStateActions_Action::VPSequencerLoadData if action.has_stringValue() => {
                    _ = load_vp_sheet(VPSheetMusicFile {
                        tracks: Arc::new(vec![action.get_stringValue().to_string()]),
                        ..Default::default()
                    }, false);
                }
                AppStateActions_Action::VPSequencerEnableSustain => {
                    unsafe {
                        if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                            sequencer.set_sustain_active(true);
                        }
                    }
                }
                AppStateActions_Action::VPSequencerDisableSustain => {
                    unsafe {
                        if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                            sequencer.set_sustain_active(false);
                        }
                    }
                }
                AppStateActions_Action::VPSequencerStop => {
                    unsafe {
                        if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                            if sequencer.is_vp_sheet {
                                sequencer.stop();
                            }
                        }
                    }
                }
                AppStateActions_Action::VPSequencerSetPlaybackSpeed if action.has_doubleValue() => {
                    unsafe {
                        if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                            sequencer.set_speed(action.get_doubleValue());
                        }
                    }
                }
                AppStateActions_Action::VPSequencerSetBPM if action.has_uint32Value() => {
                    unsafe {
                        if let Some(midi_sequencer) = MIDI_SEQUENCER.get_mut() {
                            if let Some(vp_sequencer) = VP_SHEET_SEQUENCER.get_mut() {
                                vp_sequencer.update_bpm(action.get_uint32Value() as usize);
                            }

                            if let Some(vp_sequencer) = VP_SHEET_SEQUENCER.get() {
                                let vp_file = match vp_sequencer.file.as_ref() {
                                    Some(value) => value,
                                    None => return ReduceMiddlewareResult::default(),
                                };

                                let data = vp_sequencer.to_midi(vp_file);
                                let mut c = Cursor::new(data);

                                match MidiFile::new(vp_file.file_name.clone().unwrap_or("N/A".to_string()), &mut c) {
                                    Ok(file) => {
                                        midi_sequencer.update_midi_file(&Arc::new(file));
                                        self.core_api.dispatch_midi_sequencer_effect(&pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::MidiSequencerEvent, move |effect| {
                                            let mut sequencer_event = AppMidiSequencerEvent::new();
                                            sequencer_event.set_eventType(AppMidiSequencerEventType::TOTAL_TIME_CHANGE);
                                            sequencer_event.set_totalTime(midi_sequencer.get_total_time());
                                            effect.set_midiSequencerEvent(sequencer_event);
                                        }));
                                    }
                                    _ => {}
                                }
                            }
                        }
                    }
                }
                _ => {}
            }
        }

        reduce(store, action)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::{Arc, Mutex};
    use std::collections::VecDeque;
    use reactive_state::middleware::Middleware;
    use reactive_state::{Store, Reducer, ReducerResult};
    use std::rc::Rc;
    use pianorhythm_proto::pianorhythm_app_renditions::{AppVPSequencerFileLoad, AppVPSequencerTrack};
    use pianorhythm_proto::pianorhythm_effects::AppStateEffects_Action;
    use crate::types::CoreClientApi;

    // Mock CoreClientApi for testing
    #[derive(Default)]
    struct MockCoreClientApi {
        pub dispatched_app_effects: Arc<Mutex<VecDeque<AppStateEffects>>>,
        pub dispatched_midi_sequencer_effects: Arc<Mutex<VecDeque<AppStateEffects>>>,
    }

    impl CoreClientApi for MockCoreClientApi {
        fn init(&mut self) {}

        fn ws_emit_binary(&self, _bytes: Vec<u8>) {}

        fn dispatch_app_effect(&self, effect: &AppStateEffects) {
            self.dispatched_app_effects.lock().unwrap().push_back(effect.clone());
        }

        fn dispatch_midi_sequencer_effect(&self, effect: &AppStateEffects) {
            self.dispatched_midi_sequencer_effects.lock().unwrap().push_back(effect.clone());
        }

        #[cfg(feature = "use_synth")]
        fn dispatch_midi_synth_event(&self, _event: &pianorhythm_synth::PianoRhythmSynthEvent) {}
    }

    // Simple test reducer
    struct TestReducer;
    impl Reducer<AppState, AppStateActions, AppStateEvents, AppStateEffects> for TestReducer {
        fn reduce(&self, state: &Rc<AppState>, _action: &AppStateActions) -> ReducerResult<AppState, AppStateEvents, AppStateEffects> {
            ReducerResult {
                state: state.clone(),
                events: vec![],
                effects: vec![],
            }
        }
    }

    // Helper function to create a test store
    fn create_test_store() -> Store<AppState, AppStateActions, AppStateEvents, AppStateEffects> {
        Store::new(TestReducer, AppState::new())
    }

    // Helper function to create mock middleware
    fn create_mock_middleware() -> (HandleVPSequencerActionsMiddleware<'static>, Arc<Mutex<VecDeque<AppStateEffects>>>, Arc<Mutex<VecDeque<AppStateEffects>>>) {
        let app_effects = Arc::new(Mutex::new(VecDeque::new()));
        let midi_effects = Arc::new(Mutex::new(VecDeque::new()));

        let mock_api = MockCoreClientApi {
            dispatched_app_effects: app_effects.clone(),
            dispatched_midi_sequencer_effects: midi_effects.clone(),
        };

        let boxed_api: crate::types::CoreClientApiType = Box::new(mock_api);
        let leaked_api: &'static crate::types::CoreClientApiType = Box::leak(Box::new(boxed_api));
        let middleware = HandleVPSequencerActionsMiddleware {
            core_api: leaked_api,
        };
        (middleware, app_effects, midi_effects)
    }

    // Helper function to create a dummy reduce function
    fn dummy_reduce_fn(
        _store: &Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
        _action: Option<&AppStateActions>,
    ) -> reactive_state::middleware::ReduceMiddlewareResult<AppStateEvents, AppStateEffects> {
        reactive_state::middleware::ReduceMiddlewareResult {
            events: vec![],
            effects: vec![],
        }
    }

    #[test]
    fn test_middleware_creation() {
        let (middleware, _, _) = create_mock_middleware();
        // Test that middleware can be created successfully
        assert!(!std::ptr::eq(middleware.core_api as *const _, std::ptr::null()));
    }

    #[test]
    fn test_on_reduce_with_none_action() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        // Act
        let result = middleware.on_reduce(&store, None, dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_vp_sequencer_load_data_with_empty_tracks() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerLoadData);

        let mut vp_file_load = AppVPSequencerFileLoad::new();
        vp_file_load.set_data("test data".to_string());
        vp_file_load.set_fileName("test.vp".to_string());
        // Empty tracks - should return early
        action.set_vpFileLoad(vp_file_load);

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[cfg(feature = "use_synth")]
    #[test]
    fn test_vp_sequencer_load_data_with_tracks() {
        let (middleware, app_effects, _) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerLoadData);

        let mut vp_file_load = AppVPSequencerFileLoad::new();
        vp_file_load.set_data("a b c d e f g".to_string()); // Simple VP notation
        vp_file_load.set_fileName("test.vp".to_string());

        let mut track = AppVPSequencerTrack::new();
        track.set_index(0);
        track.set_tempo(120);
        track.set_name("Track 1".to_string());
        vp_file_load.set_tracks(vec![track].into());

        action.set_vpFileLoad(vp_file_load);

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        // Note: The actual effect dispatch depends on load_vp_sheet implementation
        // which may or may not dispatch effects based on internal logic
    }

    #[cfg(feature = "use_synth")]
    #[test]
    fn test_vp_sequencer_download_as_midi() {
        let (middleware, app_effects, _) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerDownloadAsMidi);

        let mut vp_file_load = AppVPSequencerFileLoad::new();
        vp_file_load.set_data("a b c d e f g".to_string());
        vp_file_load.set_fileName("test.vp".to_string());

        let mut track = AppVPSequencerTrack::new();
        track.set_index(0);
        track.set_tempo(120);
        vp_file_load.set_tracks(vec![track].into());

        action.set_vpFileLoad(vp_file_load);

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        // Note: Effect dispatch depends on load_vp_sheet returning Some(data)
    }
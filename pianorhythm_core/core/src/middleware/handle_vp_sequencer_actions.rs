use std::io::Cursor;
use std::sync::Arc;

use reactive_state::middleware::{Middleware, ReduceFn, ReduceMiddlewareResult};

use pianorhythm_proto::pianorhythm_actions::{AppStateActions, AppStateActions_Action};
use pianorhythm_proto::pianorhythm_app_renditions::{AppMidiSequencerEvent, AppMidiSequencerEventType};
use pianorhythm_proto::pianorhythm_effects::{AppStateEffects, AppStateEffects_Action, AppStateEffects_BytesPayload};
use pianorhythm_proto::pianorhythm_events::AppStateEvents;
#[cfg(feature = "use_synth")]
use pianorhythm_synth::{MidiFile, VPSheetMusicFile};

use crate::reducers::app_state::AppState;
#[cfg(feature = "use_synth")]
use crate::{load_vp_sheet, MIDI_SEQUENCER, VP_SHEET_SEQUENCER};

pub struct HandleVPSequencerActionsMiddleware<'c> {
    pub core_api: &'c crate::types::CoreClientApiType,
}

impl<'c> Middleware<AppState, AppStateActions, AppStateEvents, AppStateEffects> for HandleVPSequencerActionsMiddleware<'c> {
    fn on_reduce(
        &self, store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>, action: Option<&AppStateActions>,
        reduce: ReduceFn<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
    ) -> ReduceMiddlewareResult<AppStateEvents, AppStateEffects> {
        #[cfg(feature = "use_synth")]
        if let Some(action) = action {
            match action.action {
                AppStateActions_Action::VPSequencerDownloadAsMidi |
                AppStateActions_Action::VPSequencerLoadData if action.has_vpFileLoad() => {
                    let file = action.get_vpFileLoad();

                    if file.get_tracks().is_empty() {
                        return ReduceMiddlewareResult::default();
                    }

                    if let Some(data) = load_vp_sheet(VPSheetMusicFile {
                        tracks: Arc::new(vec![file.get_data().to_string()]),
                        bpm: file.get_tracks().first().map(|x| x.get_tempo() as usize),
                        file_name: Some(file.get_fileName().to_string()),
                        ..Default::default()
                    }, action.action == AppStateActions_Action::VPSequencerDownloadAsMidi) {
                        self.core_api.dispatch_app_effect(&pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::MidiSequencerDownloadMidi, |effect| {
                            let mut payload = AppStateEffects_BytesPayload::new();
                            payload.set_payload(data.iter().map(|x| *x as u32).collect());
                            payload.set_id(file.get_fileName().to_string());
                            effect.set_bytesPayload(payload);
                        }));
                    };
                }
                AppStateActions_Action::VPSequencerLoadData if action.has_stringValue() => {
                    _ = load_vp_sheet(VPSheetMusicFile {
                        tracks: Arc::new(vec![action.get_stringValue().to_string()]),
                        ..Default::default()
                    }, false);
                }
                AppStateActions_Action::VPSequencerEnableSustain => {
                    unsafe {
                        if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                            sequencer.set_sustain_active(true);
                        }
                    }
                }
                AppStateActions_Action::VPSequencerDisableSustain => {
                    unsafe {
                        if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                            sequencer.set_sustain_active(false);
                        }
                    }
                }
                AppStateActions_Action::VPSequencerStop => {
                    unsafe {
                        if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                            if sequencer.is_vp_sheet {
                                sequencer.stop();
                            }
                        }
                    }
                }
                AppStateActions_Action::VPSequencerSetPlaybackSpeed if action.has_doubleValue() => {
                    unsafe {
                        if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                            sequencer.set_speed(action.get_doubleValue());
                        }
                    }
                }
                AppStateActions_Action::VPSequencerSetBPM if action.has_uint32Value() => {
                    unsafe {
                        if let Some(midi_sequencer) = MIDI_SEQUENCER.get_mut() {
                            if let Some(vp_sequencer) = VP_SHEET_SEQUENCER.get_mut() {
                                vp_sequencer.update_bpm(action.get_uint32Value() as usize);
                            }

                            if let Some(vp_sequencer) = VP_SHEET_SEQUENCER.get() {
                                let vp_file = match vp_sequencer.file.as_ref() {
                                    Some(value) => value,
                                    None => return ReduceMiddlewareResult::default(),
                                };

                                let data = vp_sequencer.to_midi(vp_file);
                                let mut c = Cursor::new(data);

                                match MidiFile::new(vp_file.file_name.clone().unwrap_or("N/A".to_string()), &mut c) {
                                    Ok(file) => {
                                        midi_sequencer.update_midi_file(&Arc::new(file));
                                        self.core_api.dispatch_midi_sequencer_effect(&pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::MidiSequencerEvent, move |effect| {
                                            let mut sequencer_event = AppMidiSequencerEvent::new();
                                            sequencer_event.set_eventType(AppMidiSequencerEventType::TOTAL_TIME_CHANGE);
                                            sequencer_event.set_totalTime(midi_sequencer.get_total_time());
                                            effect.set_midiSequencerEvent(sequencer_event);
                                        }));
                                    }
                                    _ => {}
                                }
                            }
                        }
                    }
                }
                _ => {}
            }
        }

        reduce(store, action)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::CoreClientApi;
    use pianorhythm_proto::pianorhythm_app_renditions::{AppVPSequencerFileLoad, AppVPSequencerTrack};
    use reactive_state::middleware::Middleware;
    use reactive_state::{Reducer, ReducerResult, Store};
    use std::collections::VecDeque;
    use std::rc::Rc;
    use std::sync::{Arc, Mutex};

    // Mock CoreClientApi for testing
    #[derive(Default)]
    struct MockCoreClientApi {
        pub dispatched_app_effects: Arc<Mutex<VecDeque<AppStateEffects>>>,
        pub dispatched_midi_sequencer_effects: Arc<Mutex<VecDeque<AppStateEffects>>>,
    }

    impl CoreClientApi for MockCoreClientApi {
        fn init(&mut self) {}

        fn ws_emit_binary(&self, _bytes: Vec<u8>) {}

        fn dispatch_app_effect(&self, effect: &AppStateEffects) {
            self.dispatched_app_effects.lock().unwrap().push_back(effect.clone());
        }

        fn dispatch_midi_sequencer_effect(&self, effect: &AppStateEffects) {
            self.dispatched_midi_sequencer_effects.lock().unwrap().push_back(effect.clone());
        }

        #[cfg(feature = "use_synth")]
        fn dispatch_midi_synth_event(&self, _event: &pianorhythm_synth::PianoRhythmSynthEvent) {}
    }

    // Simple test reducer
    struct TestReducer;
    impl Reducer<AppState, AppStateActions, AppStateEvents, AppStateEffects> for TestReducer {
        fn reduce(&self, state: &Rc<AppState>, _action: &AppStateActions) -> ReducerResult<AppState, AppStateEvents, AppStateEffects> {
            ReducerResult {
                state: state.clone(),
                events: vec![],
                effects: vec![],
            }
        }
    }

    // Helper function to create a test store
    fn create_test_store() -> Store<AppState, AppStateActions, AppStateEvents, AppStateEffects> {
        Store::new(TestReducer, AppState::new())
    }

    // Helper function to create mock middleware
    fn create_mock_middleware() -> (HandleVPSequencerActionsMiddleware<'static>, Arc<Mutex<VecDeque<AppStateEffects>>>, Arc<Mutex<VecDeque<AppStateEffects>>>) {
        let app_effects = Arc::new(Mutex::new(VecDeque::new()));
        let midi_effects = Arc::new(Mutex::new(VecDeque::new()));

        let mock_api = MockCoreClientApi {
            dispatched_app_effects: app_effects.clone(),
            dispatched_midi_sequencer_effects: midi_effects.clone(),
        };

        let boxed_api: crate::types::CoreClientApiType = Box::new(mock_api);
        let leaked_api: &'static crate::types::CoreClientApiType = Box::leak(Box::new(boxed_api));
        let middleware = HandleVPSequencerActionsMiddleware {
            core_api: leaked_api,
        };
        (middleware, app_effects, midi_effects)
    }

    // Helper function to create a dummy reduce function
    fn dummy_reduce_fn(
        _store: &Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
        _action: Option<&AppStateActions>,
    ) -> reactive_state::middleware::ReduceMiddlewareResult<AppStateEvents, AppStateEffects> {
        reactive_state::middleware::ReduceMiddlewareResult {
            events: vec![],
            effects: vec![],
        }
    }

    #[test]
    fn test_middleware_creation() {
        let (middleware, _, _) = create_mock_middleware();
        // Test that middleware can be created successfully
        assert!(!std::ptr::eq(middleware.core_api as *const _, std::ptr::null()));
    }

    #[test]
    fn test_on_reduce_with_none_action() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        // Act
        let result = middleware.on_reduce(&store, None, dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_vp_sequencer_load_data_with_empty_tracks() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerLoadData);

        let mut vp_file_load = AppVPSequencerFileLoad::new();
        vp_file_load.set_data("test data".to_string());
        vp_file_load.set_fileName("test.vp".to_string());
        // Empty tracks - should return early
        action.set_vpFileLoad(vp_file_load);

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[cfg(feature = "use_synth")]
    #[test]
    fn test_vp_sequencer_load_data_with_tracks() {
        let (middleware, app_effects, _) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerLoadData);

        let mut vp_file_load = AppVPSequencerFileLoad::new();
        vp_file_load.set_data("a b c d e f g".to_string()); // Simple VP notation
        vp_file_load.set_fileName("test.vp".to_string());

        let mut track = AppVPSequencerTrack::new();
        track.set_index(0);
        track.set_tempo(120);
        track.set_name("Track 1".to_string());
        vp_file_load.set_tracks(vec![track].into());

        action.set_vpFileLoad(vp_file_load);

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        // Note: The actual effect dispatch depends on load_vp_sheet implementation
        // which may or may not dispatch effects based on internal logic
    }

    #[cfg(feature = "use_synth")]
    #[test]
    fn test_vp_sequencer_download_as_midi() {
        let (middleware, app_effects, _) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerDownloadAsMidi);

        let mut vp_file_load = AppVPSequencerFileLoad::new();
        vp_file_load.set_data("a b c d e f g".to_string());
        vp_file_load.set_fileName("test.vp".to_string());

        let mut track = AppVPSequencerTrack::new();
        track.set_index(0);
        track.set_tempo(120);
        vp_file_load.set_tracks(vec![track].into());

        action.set_vpFileLoad(vp_file_load);

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        // Note: Effect dispatch depends on load_vp_sheet returning Some(data)
    }

    #[test]
    fn test_vp_sequencer_load_data_with_string_value() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerLoadData);
        action.set_stringValue("a b c d e f g".to_string());

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_vp_sequencer_enable_sustain() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerEnableSustain);

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
        // Note: This test verifies the action is handled without crashing
        // The actual sequencer state change is tested through integration tests
    }

    #[test]
    fn test_vp_sequencer_disable_sustain() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerDisableSustain);

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_vp_sequencer_stop() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerStop);

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_vp_sequencer_set_playback_speed() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerSetPlaybackSpeed);
        action.set_doubleValue(1.5); // 1.5x speed

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_vp_sequencer_set_playback_speed_without_double_value() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerSetPlaybackSpeed);
        // No doubleValue set - should be ignored

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_vp_sequencer_set_bpm() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerSetBPM);
        action.set_uint32Value(140); // 140 BPM

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_vp_sequencer_set_bpm_without_uint32_value() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerSetBPM);
        // No uint32Value set - should be ignored

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_unknown_action_is_ignored() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::Unknown);

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_non_vp_sequencer_action_is_ignored() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::EnableUI); // Non-VP sequencer action

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_vp_sequencer_load_data_without_required_fields() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerLoadData);
        // No vpFileLoad or stringValue set - should be ignored

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[cfg(feature = "use_synth")]
    #[test]
    fn test_vp_sequencer_load_data_with_multiple_tracks() {
        let (middleware, app_effects, _) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerLoadData);

        let mut vp_file_load = AppVPSequencerFileLoad::new();
        vp_file_load.set_data("a b c d e f g h i j k l".to_string());
        vp_file_load.set_fileName("multi_track.vp".to_string());

        let mut track1 = AppVPSequencerTrack::new();
        track1.set_index(0);
        track1.set_tempo(120);
        track1.set_name("Piano".to_string());

        let mut track2 = AppVPSequencerTrack::new();
        track2.set_index(1);
        track2.set_tempo(140);
        track2.set_name("Drums".to_string());

        vp_file_load.set_tracks(vec![track1, track2].into());
        action.set_vpFileLoad(vp_file_load);

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
    }

    #[test]
    fn test_multiple_actions_in_sequence() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        // Test enable sustain
        let mut action1 = AppStateActions::new();
        action1.set_action(AppStateActions_Action::VPSequencerEnableSustain);
        let result1 = middleware.on_reduce(&store, Some(&action1), dummy_reduce_fn);

        // Test set playback speed
        let mut action2 = AppStateActions::new();
        action2.set_action(AppStateActions_Action::VPSequencerSetPlaybackSpeed);
        action2.set_doubleValue(2.0);
        let result2 = middleware.on_reduce(&store, Some(&action2), dummy_reduce_fn);

        // Test disable sustain
        let mut action3 = AppStateActions::new();
        action3.set_action(AppStateActions_Action::VPSequencerDisableSustain);
        let result3 = middleware.on_reduce(&store, Some(&action3), dummy_reduce_fn);

        // Assert all results
        assert!(result1.events.is_empty() && result1.effects.is_empty());
        assert!(result2.events.is_empty() && result2.effects.is_empty());
        assert!(result3.events.is_empty() && result3.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_edge_case_values() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        // Test with zero BPM
        let mut action1 = AppStateActions::new();
        action1.set_action(AppStateActions_Action::VPSequencerSetBPM);
        action1.set_uint32Value(0);
        let result1 = middleware.on_reduce(&store, Some(&action1), dummy_reduce_fn);

        // Test with very high BPM
        let mut action2 = AppStateActions::new();
        action2.set_action(AppStateActions_Action::VPSequencerSetBPM);
        action2.set_uint32Value(999);
        let result2 = middleware.on_reduce(&store, Some(&action2), dummy_reduce_fn);

        // Test with negative playback speed
        let mut action3 = AppStateActions::new();
        action3.set_action(AppStateActions_Action::VPSequencerSetPlaybackSpeed);
        action3.set_doubleValue(-1.0);
        let result3 = middleware.on_reduce(&store, Some(&action3), dummy_reduce_fn);

        // Test with zero playback speed
        let mut action4 = AppStateActions::new();
        action4.set_action(AppStateActions_Action::VPSequencerSetPlaybackSpeed);
        action4.set_doubleValue(0.0);
        let result4 = middleware.on_reduce(&store, Some(&action4), dummy_reduce_fn);

        // Assert all handle edge cases gracefully
        assert!(result1.events.is_empty() && result1.effects.is_empty());
        assert!(result2.events.is_empty() && result2.effects.is_empty());
        assert!(result3.events.is_empty() && result3.effects.is_empty());
        assert!(result4.events.is_empty() && result4.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_vp_sequencer_actions_with_empty_file_name() {
        let (middleware, app_effects, _) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerLoadData);

        let mut vp_file_load = AppVPSequencerFileLoad::new();
        vp_file_load.set_data("a b c".to_string());
        vp_file_load.set_fileName("".to_string()); // Empty filename

        let mut track = AppVPSequencerTrack::new();
        track.set_index(0);
        track.set_tempo(120);
        vp_file_load.set_tracks(vec![track].into());

        action.set_vpFileLoad(vp_file_load);

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
    }

    #[test]
    fn test_vp_sequencer_actions_with_special_characters_in_data() {
        let (middleware, app_effects, _) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerLoadData);

        let mut vp_file_load = AppVPSequencerFileLoad::new();
        vp_file_load.set_data("a b c [chord] | pause \n new line".to_string());
        vp_file_load.set_fileName("special_chars.vp".to_string());

        let mut track = AppVPSequencerTrack::new();
        track.set_index(0);
        track.set_tempo(120);
        vp_file_load.set_tracks(vec![track].into());

        action.set_vpFileLoad(vp_file_load);

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
    }

    #[test]
    fn test_vp_sequencer_string_value_with_empty_string() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerLoadData);
        action.set_stringValue("".to_string()); // Empty string

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_vp_sequencer_string_value_with_long_content() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerLoadData);
        // Create a long VP notation string
        let long_content = "a b c d e f g h i j k l ".repeat(100);
        action.set_stringValue(long_content);

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_concurrent_action_handling() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        // Simulate concurrent actions by calling multiple actions rapidly
        let actions = vec![
            (AppStateActions_Action::VPSequencerEnableSustain, None, None),
            (AppStateActions_Action::VPSequencerSetPlaybackSpeed, Some(1.5), None),
            (AppStateActions_Action::VPSequencerSetBPM, None, Some(140)),
            (AppStateActions_Action::VPSequencerDisableSustain, None, None),
            (AppStateActions_Action::VPSequencerStop, None, None),
        ];

        for (action_type, double_val, uint_val) in actions {
            let mut action = AppStateActions::new();
            action.set_action(action_type);
            if let Some(val) = double_val {
                action.set_doubleValue(val);
            }
            if let Some(val) = uint_val {
                action.set_uint32Value(val);
            }

            let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);
            assert!(result.events.is_empty());
            assert!(result.effects.is_empty());
        }

        // Assert no effects were dispatched
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_middleware_with_different_store_states() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();

        // Test with different app states
        let states = vec![
            AppState::new(),
            AppState::default(),
        ];

        for state in states {
            let store = Store::new(TestReducer, state);

            let mut action = AppStateActions::new();
            action.set_action(AppStateActions_Action::VPSequencerEnableSustain);

            let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

            assert!(result.events.is_empty());
            assert!(result.effects.is_empty());
        }

        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_vp_sequencer_actions_with_empty_file_name() {
        let (middleware, app_effects, _) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerLoadData);

        let mut vp_file_load = AppVPSequencerFileLoad::new();
        vp_file_load.set_data("a b c".to_string());
        vp_file_load.set_fileName("".to_string()); // Empty filename

        let mut track = AppVPSequencerTrack::new();
        track.set_index(0);
        track.set_tempo(120);
        vp_file_load.set_tracks(vec![track].into());

        action.set_vpFileLoad(vp_file_load);

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
    }

    #[test]
    fn test_vp_sequencer_string_value_with_empty_string() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::VPSequencerLoadData);
        action.set_stringValue("".to_string()); // Empty string

        // Act
        let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        assert!(result.events.is_empty());
        assert!(result.effects.is_empty());
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_concurrent_action_handling() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        // Simulate concurrent actions by calling multiple actions rapidly
        let actions = vec![
            (AppStateActions_Action::VPSequencerEnableSustain, None, None),
            (AppStateActions_Action::VPSequencerSetPlaybackSpeed, Some(1.5), None),
            (AppStateActions_Action::VPSequencerSetBPM, None, Some(140)),
            (AppStateActions_Action::VPSequencerDisableSustain, None, None),
            (AppStateActions_Action::VPSequencerStop, None, None),
        ];

        for (action_type, double_val, uint_val) in actions {
            let mut action = AppStateActions::new();
            action.set_action(action_type);
            if let Some(val) = double_val {
                action.set_doubleValue(val);
            }
            if let Some(val) = uint_val {
                action.set_uint32Value(val);
            }

            let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);
            assert!(result.events.is_empty());
            assert!(result.effects.is_empty());
        }

        // Assert no effects were dispatched
        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    #[test]
    fn test_middleware_with_different_store_states() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();

        // Test with different app states
        let states = vec![
            AppState::new(),
            AppState::default(),
        ];

        for state in states {
            let store = Store::new(TestReducer, state);

            let mut action = AppStateActions::new();
            action.set_action(AppStateActions_Action::VPSequencerEnableSustain);

            let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

            assert!(result.events.is_empty());
            assert!(result.effects.is_empty());
        }

        assert!(app_effects.lock().unwrap().is_empty());
        assert!(midi_effects.lock().unwrap().is_empty());
    }

    /// Integration-style test that verifies the middleware handles all VP sequencer actions
    #[test]
    fn test_all_vp_sequencer_actions_comprehensive() {
        let (middleware, app_effects, midi_effects) = create_mock_middleware();
        let store = create_test_store();

        // Test all VP sequencer actions systematically
        let test_cases = vec![
            // Load data with VP file
            {
                let mut action = AppStateActions::new();
                action.set_action(AppStateActions_Action::VPSequencerLoadData);
                let mut vp_file_load = AppVPSequencerFileLoad::new();
                vp_file_load.set_data("a b c d".to_string());
                vp_file_load.set_fileName("test.vp".to_string());
                let mut track = AppVPSequencerTrack::new();
                track.set_index(0);
                track.set_tempo(120);
                vp_file_load.set_tracks(vec![track].into());
                action.set_vpFileLoad(vp_file_load);
                action
            },
            // Load data with string value
            {
                let mut action = AppStateActions::new();
                action.set_action(AppStateActions_Action::VPSequencerLoadData);
                action.set_stringValue("e f g h".to_string());
                action
            },
            // Download as MIDI
            {
                let mut action = AppStateActions::new();
                action.set_action(AppStateActions_Action::VPSequencerDownloadAsMidi);
                let mut vp_file_load = AppVPSequencerFileLoad::new();
                vp_file_load.set_data("i j k l".to_string());
                vp_file_load.set_fileName("download.vp".to_string());
                let mut track = AppVPSequencerTrack::new();
                track.set_index(0);
                track.set_tempo(140);
                vp_file_load.set_tracks(vec![track].into());
                action.set_vpFileLoad(vp_file_load);
                action
            },
            // Enable sustain
            {
                let mut action = AppStateActions::new();
                action.set_action(AppStateActions_Action::VPSequencerEnableSustain);
                action
            },
            // Disable sustain
            {
                let mut action = AppStateActions::new();
                action.set_action(AppStateActions_Action::VPSequencerDisableSustain);
                action
            },
            // Stop
            {
                let mut action = AppStateActions::new();
                action.set_action(AppStateActions_Action::VPSequencerStop);
                action
            },
            // Set playback speed
            {
                let mut action = AppStateActions::new();
                action.set_action(AppStateActions_Action::VPSequencerSetPlaybackSpeed);
                action.set_doubleValue(1.25);
                action
            },
            // Set BPM
            {
                let mut action = AppStateActions::new();
                action.set_action(AppStateActions_Action::VPSequencerSetBPM);
                action.set_uint32Value(160);
                action
            },
        ];

        // Execute all test cases
        for action in test_cases {
            let result = middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

            // All actions should complete without errors
            assert!(result.events.is_empty());
            assert!(result.effects.is_empty());
        }

        // Verify that the middleware handled all actions gracefully
        // Note: Actual effect dispatch depends on global sequencer state and load_vp_sheet implementation
        println!("App effects dispatched: {}", app_effects.lock().unwrap().len());
        println!("MIDI effects dispatched: {}", midi_effects.lock().unwrap().len());
    }
}